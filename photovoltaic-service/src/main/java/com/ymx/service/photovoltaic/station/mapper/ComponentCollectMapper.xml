<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.ymx.service.photovoltaic.station.mapper.ComponentCollectMapper">

	<resultMap id="componentViewResultMap" type="com.ymx.service.photovoltaic.station.model.ComponentViewModel">
		<result column="chip_id" property="chipId"/>
		<result column="temperature" property="componentTemperature"/>
		<result column="mos_temperature" property="mosTemperature"/>
		<result column="output_current" property="outputCurrent"/>
		<result column="output_voltage" property="outputVoltage"/>
		<result column="collect_time" property="collectTime"/>
		<result column="kwh" property="kwh"/>
	</resultMap>

	<resultMap id="componentElectricResultMap" type="com.ymx.service.photovoltaic.station.model.ComponentElectricModel">
		<result column="chip_id" property="chipId"/>
		<result column="temperature" property="componentTemperature"/>
		<result column="mos_temperature" property="mosTemperature"/>
		<result column="output_current" property="outputCurrent"/>
		<result column="output_voltage" property="outputVoltage"/>
	</resultMap>

	<!-- 检查mos_temperature字段是否存在 -->
	<select id="checkMosTemperatureColumnExists" parameterType="HashMap" resultType="java.lang.Integer">
		SELECT COUNT(1) FROM information_schema.COLUMNS
		WHERE TABLE_SCHEMA = DATABASE()
		AND TABLE_NAME = CONCAT('t_component_collect', #{tableName})
		AND COLUMN_NAME = 'mos_temperature'
	</select>


	<select id="deleteTable" resultType="java.lang.String">
		drop table if exists t_component_collect_${powerStationId};
	</select>

	<select id="createCollectTable" resultType="java.lang.String">
		CREATE TABLE `t_component_collect_${powerStationId}` (
		`id` bigint unsigned NOT NULL AUTO_INCREMENT,
		`chip_id` varchar(10) NOT NULL,
		`temperature` smallint DEFAULT NULL,
		`output_current` smallint unsigned DEFAULT NULL,
		`output_voltage` smallint unsigned DEFAULT NULL,
		`input_current` smallint unsigned DEFAULT NULL,
		`input_voltage` smallint unsigned DEFAULT NULL,
		`pwm` tinyint unsigned DEFAULT NULL,
		`rssi` tinyint unsigned DEFAULT NULL,
		`tick` smallint unsigned DEFAULT NULL,
		`collect_time` datetime NOT NULL,
		`create_time` datetime NOT NULL,
		PRIMARY KEY (`id`),
		KEY `idx_collect_time` (`collect_time`) USING BTREE
		) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMPRESSION='zlib' COMMENT='${powerStationId}组件采集表';
	</select>

	<select id="createPLCCollectTable" resultType="java.lang.String">
		CREATE TABLE `t_component_collect_${powerStationId}` (
		`id` bigint unsigned NOT NULL AUTO_INCREMENT,
		`chip_id` varchar(10) NOT NULL,
		`temperature` tinyint DEFAULT NULL,
		`mos_temperature` tinyint DEFAULT NULL,
		`out_temperature` tinyint DEFAULT NULL,
		`output_current` smallint unsigned DEFAULT NULL,
		`output_voltage` smallint unsigned DEFAULT NULL,
		`input_current` smallint unsigned DEFAULT NULL,
		`input_voltage` smallint unsigned DEFAULT NULL,
		`pwm` tinyint unsigned DEFAULT NULL,
		`rssi` tinyint unsigned DEFAULT NULL,
		`tick` smallint unsigned DEFAULT NULL,
		`collect_time` datetime NOT NULL,
		`create_time` datetime NOT NULL,
		PRIMARY KEY (`id`),
		KEY `idx_collect_time` (`collect_time`) USING BTREE
		) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMPRESSION='zlib' COMMENT='${powerStationId}组件采集表';
	</select>


	<select id="createGroupCollectTable" resultType="java.lang.String">
		CREATE TABLE `t_group_collect_${powerStationId}` (
		`id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
		`groupId` varchar(64) DEFAULT NULL,
		`batchNo` varchar(32) DEFAULT NULL,
		`avgOutputCurrent` decimal(10,4) DEFAULT NULL,
		`sumOutputVoltage` decimal(10,4) DEFAULT NULL,
		PRIMARY KEY (`id`),
		KEY `idx_batchNo` (`batchNo`(8)) USING BTREE
		) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMPRESSION='zlib' COMMENT='${powerStationId}组串采集表';
	</select>


	<!--查询电站的所有采集信息-->
	<select id="queryComponentCollectListByComId" parameterType="HashMap" resultType="ComponentCollect">
		<if test="hasMosTemperature != null and hasMosTemperature == 1">
			select c.id,c.chip_id as chipId ,c.temperature as componentTemperature,
			c.mos_temperature as mosTemperature,
			c.output_voltage as outputVoltage,c.output_current as outputCurrent,c.input_current as inputCurrent,
			c.input_voltage as inputVoltage,
			e.kwh as kwh,
			c.collect_time as  createTime from t_component_collect${tableName} c
			left join t_app_component component on c.chip_id=component.chipId
			left join t_component_energy e on c.chip_id=e.chip_id
			where  component.powerStationId=#{powerStationId}
			<if test="createTimeStart != null and createTimeStart != ''">
				and c.collect_time >= #{createTimeStart}
			</if>
			<if test="createTimeEnd != null and createTimeEnd != ''">
				<![CDATA[ and c.collect_time < #{createTimeEnd} ]]>
			</if>
			order by c.collect_time
			<if test="typeCount != null and typeCount != ''">
				<![CDATA[ limit ${typeCount} ]]>
			</if>
		</if>
		<if test="hasMosTemperature == null or hasMosTemperature == 0">
			select c.id,c.chip_id as chipId ,c.temperature as componentTemperature,
			NULL as mosTemperature,
			c.output_voltage as outputVoltage,c.output_current as outputCurrent,c.input_current as inputCurrent,
			c.input_voltage as inputVoltage,
			e.kwh as kwh,
			c.collect_time as  createTime from t_component_collect${tableName} c
			left join t_app_component component on c.chip_id=component.chipId
			left join t_component_energy e on c.chip_id=e.chip_id
			where  component.powerStationId=#{powerStationId}
			<if test="createTimeStart != null and createTimeStart != ''">
				and c.collect_time >= #{createTimeStart}
			</if>
			<if test="createTimeEnd != null and createTimeEnd != ''">
				<![CDATA[ and c.collect_time < #{createTimeEnd} ]]>
			</if>
			order by c.collect_time
			<if test="typeCount != null and typeCount != ''">
				<![CDATA[ limit ${typeCount} ]]>
			</if>
		</if>
	</select>


	<select id="queryPastCollectInfo" parameterType="HashMap" resultMap="componentViewResultMap">
		<if test="hasMosTemperature != null and hasMosTemperature == 1">
			select c.chip_id,c.temperature,c.mos_temperature,c.output_voltage,c.output_current,c.collect_time,e.kwh
			from t_component_collect${tableName} c
			left join t_component_energy e on c.chip_id=e.chip_id
			where c.collect_time >= #{collectTimeStart} and <![CDATA[ c.collect_time < #{collectTimeEnd} ]]>
			<if test="null != chipIdList">
				and c.chip_id in
				<foreach collection="chipIdList" item="item" open="(" close=")" separator=",">
					#{item}
				</foreach>
			</if>
		</if>
		<if test="hasMosTemperature == null or hasMosTemperature == 0">
			select c.chip_id,c.temperature,NULL as mos_temperature,c.output_voltage,c.output_current,c.collect_time,e.kwh
			from t_component_collect${tableName} c
			left join t_component_energy e on c.chip_id=e.chip_id
			where c.collect_time >= #{collectTimeStart} and <![CDATA[ c.collect_time < #{collectTimeEnd} ]]>
			<if test="null != chipIdList">
				and c.chip_id in
				<foreach collection="chipIdList" item="item" open="(" close=")" separator=",">
					#{item}
				</foreach>
			</if>
		</if>
	</select>

	<select id="queryPastCollectInfoTo" parameterType="HashMap" resultMap="componentViewResultMap">
		select chip_id,temperature,
		output_voltage,output_current,collect_time
		from t_component_collect${tableName}
		where collect_time >= #{collectTimeStart} and <![CDATA[ collect_time < #{collectTimeEnd} ]]>
		<if test="null != chipIdList">
			and chip_id in
			<foreach collection="chipIdList" item="item" open="(" close=")" separator=",">
				#{item}
			</foreach>
		</if>
	</select>

	<select id="queryGroupPastCollectInfo" parameterType="HashMap" resultMap="componentElectricResultMap">
		<if test="hasMosTemperature != null and hasMosTemperature == 1">
			select e.chip_id,e.temperature,e.mos_temperature,e.output_voltage,e.output_current
			from t_component_collect${tableName} e,t_app_component c
			where e.chip_id=c.chipId and e.collect_time >= #{collectTimeStart} and <![CDATA[ e.collect_time < #{collectTimeEnd} ]]>
			and c.belongsGroupId = #{groupId}
		</if>
		<if test="hasMosTemperature == null or hasMosTemperature == 0">
			select e.chip_id,e.temperature,NULL as mos_temperature,e.output_voltage,e.output_current
			from t_component_collect${tableName} e,t_app_component c
			where e.chip_id=c.chipId and e.collect_time >= #{collectTimeStart} and <![CDATA[ e.collect_time < #{collectTimeEnd} ]]>
			and c.belongsGroupId = #{groupId}
		</if>
	</select>

	<select id="queryLastCollectInfo" parameterType="HashMap" resultMap="componentViewResultMap">
		<if test="hasMosTemperature != null and hasMosTemperature == 1">
			select chip_id,temperature,mos_temperature,output_voltage,output_current,collect_time
			from t_component_collect${tableName}
			where collect_time>= #{collectTimeStart}
			<if test="null != chipIdList">
				and chip_id in
				<foreach collection="chipIdList" item="item" open="(" close=")" separator=",">
					#{item}
				</foreach>
			</if>
			order by collect_time desc limit #{limit}
		</if>
		<if test="hasMosTemperature == null or hasMosTemperature == 0">
			select chip_id,temperature,NULL as mos_temperature,output_voltage,output_current,collect_time
			from t_component_collect${tableName}
			where collect_time>= #{collectTimeStart}
			<if test="null != chipIdList">
				and chip_id in
				<foreach collection="chipIdList" item="item" open="(" close=")" separator=",">
					#{item}
				</foreach>
			</if>
			order by collect_time desc limit #{limit}
		</if>
	</select>

	<select id="queryLastCollectInfoTo" parameterType="HashMap" resultMap="componentViewResultMap">
		select chip_id,temperature,
		output_voltage,output_current,collect_time
		from t_component_collect${tableName}
		where collect_time>= #{collectTimeStart}
		<if test="null != chipIdList">
			and chip_id in
			<foreach collection="chipIdList" item="item" open="(" close=")" separator=",">
				#{item}
			</foreach>
		</if>
		order by collect_time desc limit #{limit}
	</select>


	<!--按照批次查询到所有批次-->
	<select id="queryComponentCollectStatistics" parameterType="HashMap" resultType="java.lang.Double">
		select sum(outputCurrent/1000.0 *outputVoltage/1000.0)/60/1000  from et_app_component_collect collect
		left join t_app_component component on collect.componentId=component.id
		where  component.powerStationId=#{powerStationId}
		<if test="createTimeStart != null and createTimeStart != ''">
			and collect.createTime > #{createTimeStart}
		</if>
		<if test="createTimeEnd != null and createTimeEnd != ''">
			<![CDATA[ and collect.createTime < #{createTimeEnd} ]]>
		</if>
	</select>

	<!-- 新增采集信息数据 -->
	<insert id="saveComponentCollectList" parameterType="HashMap">
		insert into t_component_collect${tableName}
		(
		chip_id,
		temperature ,
		input_current ,
		input_voltage ,
		output_current ,
		output_voltage ,
		pwm,
		rssi,
		tick,
		collect_time,
		create_time
		)
		values
		<foreach collection="list" item="item"  index= "index" separator=",">
			(
			#{item.chipId},
			#{item.componentTemperature} ,
			#{item.inputCurrent} ,
			#{item.inputVoltage} ,
			#{item.outputCurrent},
			#{item.outputVoltage},
			#{item.pwm},
			#{item.rssi},
			#{item.tick},
			#{item.collectTime},
			#{item.createTime}
			)
		</foreach>
	</insert>


	<insert id="saveNewComponentCollectList" parameterType="HashMap">
		insert into t_component_collect${tableName}
		(
		chip_id,
		temperature ,
		mos_temperature ,
		input_current ,
		input_voltage ,
		output_current ,
		output_voltage ,
		pwm,
		rssi,
		tick,
		collect_time,
		create_time
		)
		values
		<foreach collection="list" item="item"  index= "index" separator=",">
			(
			#{item.chipId},
			#{item.componentTemperature} ,
			#{item.mosTemperature} ,
			#{item.inputCurrent} ,
			#{item.inputVoltage} ,
			#{item.outputCurrent},
			#{item.outputVoltage},
			#{item.pwm},
			#{item.rssi},
			#{item.tick},
			#{item.collectTime},
			#{item.createTime}
			)
		</foreach>
	</insert>

	<!-- 新协议新增采集信息数据 -->
	<insert id="saveCollectList" parameterType="HashMap">
		insert into t_component_collect${tableName}
		(
		chip_id,
		temperature ,
		mos_temperature,
		out_temperature,
		input_current ,
		input_voltage ,
		output_current ,
		output_voltage ,
		pwm,
		rssi,
		tick,
		collect_time,
		create_time
		)
		values
		<foreach collection="list" item="item"  index= "index" separator=",">
			(
			#{item.chipId},
			#{item.componentTemperature} ,
			#{item.mosTemperature} ,
			#{item.outTemperature} ,
			#{item.inputCurrent} ,
			#{item.inputVoltage} ,
			#{item.outputCurrent},
			#{item.outputVoltage},
			#{item.pwm},
			#{item.rssi},
			#{item.tick},
			#{item.collectTime},
			now()
			)
		</foreach>
	</insert>
</mapper>