package com.ymx.service.photovoltaic.station.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import org.apache.ibatis.type.Alias;

import java.io.Serializable;

@Alias("ComponentViewModel")
public class ComponentViewModel implements Serializable {
	private String chipId;
	private String groupId;
	//hv 1:竖 2:横
	private String hv;
	private String gapTop;
	private String gapLeft;
	private Integer componentTemperature;
	private Integer mosTemperature;
	private Integer outputCurrent;
	private Integer outputVoltage;
	private Integer status;
	private String batchNo;
	private String collectTime;
	private String equipmentId;
	//加上功率
	private Integer power;
	//加上新增属性
	private String xzjd;
	private String btmd;
	private String bkbj;
	// 千瓦时
	private Double kwh;

	public ComponentViewModel() {
	}

	public ComponentViewModel(ComponentControlModel comControlModel,ComponentViewModel comViewModel) {
		this.chipId=comControlModel.getChipId();
		this.groupId=comControlModel.getGroupId();
		this.hv=comControlModel.getHv();
		this.gapTop=comControlModel.getGapTop();
		this.gapLeft=comControlModel.getGapLeft();
		this.status=comControlModel.getStatus();
		this.equipmentId=comControlModel.getEquipmentId();
		this.bkbj = comControlModel.getBkbj();
		this.btmd = comControlModel.getBtmd ();
		this.xzjd = comControlModel.getXzjd ();
		if(comViewModel!=null)
		{
			this.componentTemperature=comViewModel.getComponentTemperature();
			this.mosTemperature=comViewModel.getMosTemperature();
			this.outputCurrent=comViewModel.getOutputCurrent();
			this.outputVoltage=comViewModel.getOutputVoltage();
			this.power = comViewModel.getPower();
			this.kwh=comViewModel.getKwh();
		}

	}

	public String getCollectTime() {
		return collectTime;
	}

	public Integer getPower() {
		return power;
	}

	public void setPower(Integer power) {
		this.power = power;
	}

	public void setCollectTime(String collectTime) {
		if (collectTime.contains(".0")) {
			this.collectTime = collectTime.replace(".0", "");
		} else {
			this.collectTime = collectTime;
		}
	}

	public String getChipId() {
		return chipId;
	}

	public void setChipId(String chipId) {
		this.chipId = chipId;
	}

	public String getGroupId() {
		return groupId;
	}

	public void setGroupId(String groupId) {
		this.groupId = groupId;
	}

	public String getHv() {
		return hv;
	}

	public void setHv(String hv) {
		this.hv = hv;
	}

	public String getGapTop() {
		return gapTop;
	}

	public void setGapTop(String gapTop) {
		this.gapTop = gapTop;
	}

	public String getGapLeft() {
		return gapLeft;
	}

	public void setGapLeft(String gapLeft) {
		this.gapLeft = gapLeft;
	}

	public Integer getComponentTemperature() {
		return componentTemperature;
	}

	public void setComponentTemperature(Integer componentTemperature) {
		this.componentTemperature = componentTemperature;
	}

	public Integer getMosTemperature() {
		return mosTemperature;
	}

	public void setMosTemperature(Integer mosTemperature) {
		this.mosTemperature = mosTemperature;
	}

	public Integer getOutputCurrent() {
		return outputCurrent;
	}

	public void setOutputCurrent(Integer outputCurrent) {
		this.outputCurrent = outputCurrent;
	}

	public Integer getOutputVoltage() {
		return outputVoltage;
	}

	public void setOutputVoltage(Integer outputVoltage) {
		this.outputVoltage = outputVoltage;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	@JsonIgnore
	public String getBatchNo() {
		return batchNo;
	}

	public void setBatchNo(String batchNo) {
		this.batchNo = batchNo;
	}

	public String getEquipmentId() {
		return equipmentId;
	}

	public void setEquipmentId(String equipmentId) {
		this.equipmentId = equipmentId;
	}

	public String getXzjd() {
		return xzjd;
	}

	public void setXzjd(String xzjd) {
		this.xzjd = xzjd;
	}

	public String getBtmd() {
		return btmd;
	}

	public void setBtmd(String btmd) {
		this.btmd = btmd;
	}

	public String getBkbj() {
		return bkbj;
	}

	public void setBkbj(String bkbj) {
		this.bkbj = bkbj;
	}

	public Double getKwh() {
		return kwh;
	}

	public void setKwh(Double kwh) {
		this.kwh = kwh;
	}
}
