package com.ymx.service.photovoltaic.station.model;

import com.ymx.common.common.annotation.ChinaVoAttribute;
import org.apache.ibatis.type.Alias;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.io.Serializable;
import java.util.Date;

/**
 * 一个电站 1000个组件
 *  按 1分钟采集一次 1天  144万条  一个月 4320万
 *  按 5分钟采集一次 1天  28.8万条  一年 864万
 *  正常  最多保存1个月数据
 * @DESC 组件采集
 * @DATE 2018/8/24
 * @NAME ComponentCollect
 * @MOUDELNAME 模块
 */
@Alias("ComponentCollect")
public class ComponentCollect implements Serializable {
    // 主键
    private String id;
    // 组件表主键
    private String componentId;
    // 芯片的ID，组件唯一标示
    private String chipId;
    // 芯片的ID，采集器唯一标示
    private String imei;
    // 类型 1 输入电压 , 2 输入电流 , 3 输出电压 , 4 输出电流 , 5 组件温度
    private Integer type;

    // 组件温度 (新协议中为意美旭芯片温度)
    private Integer componentTemperature;
    // mos温度
    private Integer mosTemperature;
    // 输出端二级管温度
    private Integer outTemperature;
    // 输出电流
    private Integer outputCurrent;
    // 输出电压
    private Integer outputVoltage;
    // 输入电流
    private Integer inputCurrent;
    // 输入电压
    private Integer inputVoltage;

    // 内容
    private String content;
    // 批次号
    private String batchNo;
	// 批次号
	private String tempBatchNo;

    // 创建时间
    @ChinaVoAttribute(enField = true,columnType = "Date",column="createTimeCh")
    private Date createTime;
    private String createTimeCh;
	//功率
    private Double power;
    //获取数据状态  1成功，2，失败，3通信异常
	private Integer status;
	// 本组串输出最大电流   串联用
	private Integer maxOutputCurrent;
	// 本组串输出最大电压   并联用
	private Integer maxOutputVoltage;
	// 系统设备id
	private String powerStationId;
	private Date lastTime;

    // 优化器的pwm占空比
    private Integer pwm;
    // 优化器的通信信号强度指示
    private Integer rssi;
    // 优化器的开机计数值，单位是秒
    private Integer tick;
    // 组串id
    private String groupId;
    // 数据的采集时间
    private Date collectTime;
    // 千瓦时
    private Double kwh;

    public ComponentCollect() {
    }

    public ComponentCollect(SocketModel socketModel) {
        this.setComponentTemperature(socketModel.getComponentTemperature());
        if(socketModel.getCmd()[0]==102)
        {
        this.setMosTemperature(socketModel.getMosTemperature());
        }
        this.setOutputCurrent(socketModel.getOutputCurrent());
        this.setOutputVoltage(socketModel.getOutputVoltage());
        this.setInputCurrent(socketModel.getInputCurrent());
        this.setInputVoltage(socketModel.getInputVoltage());
        this.setPwm(socketModel.getPwm());
        this.setRssi(socketModel.getRssi());
        this.setTick(socketModel.getTick());
        this.setChipId(socketModel.getChipIdString());

        this.setStatus(socketModel.getStatus());
        this.setTempBatchNo(socketModel.getRsvString());
        BigDecimal power= BigDecimal.valueOf((this.getOutputCurrent() / 1000d)
                * (this.getOutputVoltage() / 1000d)).setScale(3 , RoundingMode.HALF_UP);
        this.setPower(power.doubleValue());

    }

    public void computePower()
    {
        BigDecimal power= BigDecimal.valueOf((this.getOutputCurrent() / 1000d)
                * (this.getOutputVoltage() / 1000d)).setScale(3 , RoundingMode.HALF_UP);
        this.setPower(power.doubleValue());
    }

    public Date getCollectTime() {
        return collectTime;
    }

    public void setCollectTime(Date collectTime) {
        this.collectTime = collectTime;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

	public String getTempBatchNo() {
		return tempBatchNo;
	}

	public void setTempBatchNo(String tempBatchNo) {
		this.tempBatchNo = tempBatchNo;
	}

	public String getPowerStationId() {
		return powerStationId;
	}

	public void setPowerStationId(String powerStationId) {
		this.powerStationId = powerStationId;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Integer getMaxOutputVoltage() {
		return maxOutputVoltage;
	}

	public void setMaxOutputVoltage(Integer maxOutputVoltage) {
		this.maxOutputVoltage = maxOutputVoltage;
	}

	public Integer getMaxOutputCurrent() {
		return maxOutputCurrent;
	}

	public void setMaxOutputCurrent(Integer maxOutputCurrent) {
		this.maxOutputCurrent = maxOutputCurrent;
	}

	public Double getPower() {
		return power;
	}

	public void setPower(Double power) {
		this.power = power;
	}

	public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getComponentId() {
        return componentId;
    }

    public void setComponentId(String componentId) {
        this.componentId = componentId;
    }

    public String getChipId() {
        return chipId;
    }

    public void setChipId(String chipId) {
        this.chipId = chipId;
    }

    public String getImei() {
        return imei;
    }

    public void setImei(String imei) {
        this.imei = imei;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getComponentTemperature() {
        return componentTemperature;
    }

    public void setComponentTemperature(Integer componentTemperature) {
        this.componentTemperature = componentTemperature;
    }

    public Integer getMosTemperature() {
        return mosTemperature;
    }

    public void setMosTemperature(Integer mosTemperature) {
        this.mosTemperature = mosTemperature;
    }

    public Integer getOutTemperature() {
        return outTemperature;
    }

    public void setOutTemperature(Integer outTemperature) {
        this.outTemperature = outTemperature;
    }

    public Integer getOutputCurrent() {
        return outputCurrent;
    }

    public void setOutputCurrent(Integer outputCurrent) {
        this.outputCurrent = outputCurrent;
    }

    public Integer getOutputVoltage() {
        return outputVoltage;
    }

    public void setOutputVoltage(Integer outputVoltage) {
        this.outputVoltage = outputVoltage;
    }

    public Integer getInputCurrent() {
        return inputCurrent;
    }

    public void setInputCurrent(Integer inputCurrent) {
        this.inputCurrent = inputCurrent;
    }

    public Integer getInputVoltage() {
        return inputVoltage;
    }

    public void setInputVoltage(Integer inputVoltage) {
        this.inputVoltage = inputVoltage;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreateTimeCh() {
        return createTimeCh;
    }

    public void setCreateTimeCh(String createTimeCh) {
        this.createTimeCh = createTimeCh;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

	public Date getLastTime() {
		return lastTime;
	}

	public void setLastTime(Date lastTime) {
		this.lastTime = lastTime;
	}

    public Integer getPwm() {
        return pwm;
    }

    public void setPwm(Integer pwm) {
        this.pwm = pwm;
    }

    public Integer getRssi() {
        return rssi;
    }

    public void setRssi(Integer rssi) {
        this.rssi = rssi;
    }

    public Integer getTick() {
        return tick;
    }

    public void setTick(Integer tick) {
        this.tick = tick;
    }

    public Double getKwh() {
        return kwh;
    }

    public void setKwh(Double kwh) {
        this.kwh = kwh;
    }

    @Override
	public String toString() {
		return "ComponentCollect{" +
				"id='" + id + '\'' +
				", componentId='" + componentId + '\'' +
				", chipId='" + chipId + '\'' +
				", imei='" + imei + '\'' +
				", type=" + type +
				", componentTemperature=" + componentTemperature +
				", outputCurrent=" + outputCurrent +
				", outputVoltage=" + outputVoltage +
				", inputCurrent=" + inputCurrent +
				", inputVoltage=" + inputVoltage +
                ", pwm=" + pwm +
                ", rssi=" + rssi +
                ", tick=" + tick +
				", content='" + content + '\'' +
				", batchNo='" + batchNo + '\'' +
				", createTime=" + createTime +
				", createTimeCh='" + createTimeCh + '\'' +
				", power=" + power +
				", status=" + status +
				", maxOutputCurrent=" + maxOutputCurrent +
				", maxOutputVoltage=" + maxOutputVoltage +
				'}';
	}
}
