package com.ymx.service.photovoltaic.station.model;

import org.apache.ibatis.type.Alias;

import java.io.Serializable;

@Alias("ComponentElectricModel")
public class ComponentElectricModel implements Serializable {
	private String chipId;
	private Integer componentTemperature;
	private Integer mosTemperature;
	private Integer outputCurrent;
	private Integer outputVoltage;
	// 千瓦时
	private Double kwh;

	public String getChipId() {
		return chipId;
	}

	public void setChipId(String chipId) {
		this.chipId = chipId;
	}

	public Integer getComponentTemperature() {
		return componentTemperature;
	}

	public void setComponentTemperature(Integer componentTemperature) {
		this.componentTemperature = componentTemperature;
	}

	public Integer getMosTemperature() {
		return mosTemperature;
	}

	public void setMosTemperature(Integer mosTemperature) {
		this.mosTemperature = mosTemperature;
	}

	public Integer getOutputCurrent() {
		return outputCurrent;
	}

	public void setOutputCurrent(Integer outputCurrent) {
		this.outputCurrent = outputCurrent;
	}

	public Integer getOutputVoltage() {
		return outputVoltage;
	}

	public void setOutputVoltage(Integer outputVoltage) {
		this.outputVoltage = outputVoltage;
	}

	public Double getKwh() {
		return kwh;
	}

	public void setKwh(Double kwh) {
		this.kwh = kwh;
	}
}
